<?php

namespace App\Entity;

use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`user`')]
#[ORM\UniqueConstraint(name: 'UNIQ_IDENTIFIER_USERNAME', fields: ['email'])]
class User implements UserInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['user:read', 'mission:users'])]
    private ?int $id = null;

    #[ORM\Column(length: 100)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    #[Groups(['user:read', 'user:write', 'mission:users'])]
    private ?string $nom = null;

    #[ORM\Column(length: 100)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    #[Groups(['user:read', 'user:write', 'mission:users'])]
    private ?string $prenom = null;

    #[ORM\Column(length: 180, unique: true)]
    #[Assert\NotBlank]
    #[Assert\Email]
    #[Groups(['user:read', 'user:write'])]
    private ?string $email = null;

    #[ORM\Column(nullable: true)]
    #[Assert\Range(min: 0, max: 168)]
    #[Groups(['user:read', 'user:write'])]
    private ?float $horaireHebdo = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?bool $forfaitJour = false;

    #[ORM\Column(length: 20, nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?string $telephone = null;

    #[ORM\Column(type: 'date', nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?\DateTimeInterface $dateEmbauche = null;

    #[ORM\Column(type: 'date', nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?\DateTimeInterface $dateDepart = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?bool $actif = true;

    #[ORM\Column(type: 'text', nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?string $notes = null;

    // Nouveaux champs ajoutés pour l'intégration
    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?string $secteur = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?string $username = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?string $manager = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?string $titre = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?bool $isManager = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?bool $vpn = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?string $mobile = null;

    /**
     * @var list<string> The user roles
     */
    #[ORM\Column]
    #[Groups(['user:read', 'user:write'])]
    private array $roles = [];

    #[ORM\Column(nullable: true)]
    #[Groups(['user:read', 'user:write'])]
    private ?bool $userOsi = false;

    /**
     * @var Collection<int, Mission>
     */
    #[ORM\ManyToMany(targetEntity: Mission::class, mappedBy: 'users')]
    #[Groups(['user:detail'])]
    private Collection $missions;

    /**
     * @var Collection<int, Segment>
     */
    #[ORM\OneToMany(targetEntity: Segment::class, mappedBy: 'user')]
    #[Groups(['user:detail'])]
    private Collection $segments;

    // Note: Les semaines de travail sont maintenant calculées dynamiquement depuis les segments

    public function __construct()
    {
        $this->missions = new ArrayCollection();
        $this->segments = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): static
    {
        $this->nom = $nom;
        return $this;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setPrenom(string $prenom): static
    {
        $this->prenom = $prenom;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;
        return $this;
    }

    public function getHoraireHebdo(): ?float
    {
        return $this->horaireHebdo;
    }

    public function setHoraireHebdo(?float $horaireHebdo): static
    {
        $this->horaireHebdo = $horaireHebdo;
        return $this;
    }

    public function isForfaitJour(): ?bool
    {
        return $this->forfaitJour;
    }

    public function setForfaitJour(?bool $forfaitJour): static
    {
        $this->forfaitJour = $forfaitJour;
        return $this;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(?string $telephone): static
    {
        $this->telephone = $telephone;
        return $this;
    }

    public function getDateEmbauche(): ?\DateTimeInterface
    {
        return $this->dateEmbauche;
    }

    public function setDateEmbauche(?\DateTimeInterface $dateEmbauche): static
    {
        $this->dateEmbauche = $dateEmbauche;
        return $this;
    }

    public function getDateDepart(): ?\DateTimeInterface
    {
        return $this->dateDepart;
    }

    public function setDateDepart(?\DateTimeInterface $dateDepart): static
    {
        $this->dateDepart = $dateDepart;
        return $this;
    }

    public function isActif(): ?bool
    {
        return $this->actif;
    }

    public function setActif(?bool $actif): static
    {
        $this->actif = $actif;
        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): static
    {
        $this->notes = $notes;
        return $this;
    }

    /**
     * @return Collection<int, Mission>
     */
    public function getMissions(): Collection
    {
        return $this->missions;
    }

    public function addMission(Mission $mission): static
    {
        if (!$this->missions->contains($mission)) {
            $this->missions->add($mission);
            $mission->addUser($this);
        }
        return $this;
    }

    public function removeMission(Mission $mission): static
    {
        if ($this->missions->removeElement($mission)) {
            $mission->removeUser($this);
        }
        return $this;
    }

    /**
     * @return Collection<int, Segment>
     */
    public function getSegments(): Collection
    {
        return $this->segments;
    }

    public function addSegment(Segment $segment): static
    {
        if (!$this->segments->contains($segment)) {
            $this->segments->add($segment);
            $segment->setUser($this);
        }
        return $this;
    }

    public function removeSegment(Segment $segment): static
    {
        if ($this->segments->removeElement($segment)) {
            if ($segment->getUser() === $this) {
                $segment->setUser(null);
            }
        }
        return $this;
    }

    // Les heures sont calculées dynamiquement depuis les segments

    #[Groups(['user:read', 'mission:users'])]
    public function getNomComplet(): string
    {
        return $this->prenom . ' ' . $this->nom;
    }

    public function __toString(): string
    {
        return $this->getNomComplet();
    }

    // Nouveaux getters et setters pour les champs ajoutés

    public function getSecteur(): ?string
    {
        return $this->secteur;
    }

    public function setSecteur(?string $secteur): static
    {
        $this->secteur = $secteur;
        return $this;
    }

    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(?string $username): static
    {
        $this->username = $username;
        return $this;
    }

    public function getManager(): ?string
    {
        return $this->manager;
    }

    public function setManager(?string $manager): static
    {
        $this->manager = $manager;
        return $this;
    }

    public function getTitre(): ?string
    {
        return $this->titre;
    }

    public function setTitre(?string $titre): static
    {
        $this->titre = $titre;
        return $this;
    }

    public function isManager(): ?bool
    {
        return $this->isManager;
    }

    public function getIsManager(): ?bool
    {
        return $this->isManager;
    }

    public function setIsManager(?bool $isManager): static
    {
        $this->isManager = $isManager;
        return $this;
    }

    public function isVpn(): ?bool
    {
        return $this->vpn;
    }

    public function setVpn(?bool $vpn): static
    {
        $this->vpn = $vpn;
        return $this;
    }

    public function getMobile(): ?string
    {
        return $this->mobile;
    }

    public function setMobile(?string $mobile): static
    {
        $this->mobile = $mobile;
        return $this;
    }

    public function isUserOsi(): ?bool
    {
        return $this->userOsi;
    }

    public function setUserOsi(?bool $userOsi): static
    {
        $this->userOsi = $userOsi;
        return $this;
    }

    // Implémentation de UserInterface

    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    /**
     * @see UserInterface
     *
     * @return list<string>
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';

        return array_unique($roles);
    }

    /**
     * @param list<string> $roles
     */
    public function setRoles(array $roles): static
    {
        $this->roles = $roles;
        return $this;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    // Méthodes utilitaires pour les rôles

    public function hasAnyRole(array $roles): bool
    {
        foreach ($roles as $role) {
            if (in_array($role, $this->getRoles(), true)) {
                return true;
            }
        }
        return false;
    }

    public function hasManagerRole(): bool
    {
        foreach ($this->getRoles() as $role) {
            if (stripos($role, 'MANAGER') !== false) {
                return true;
            }
        }
        return false;
    }

    public function hasManagerRoleOrAnyRole(array $roles): bool
    {
        // Vérifie si un rôle contient "manager"
        if ($this->hasManagerRole()) {
            return true;
        }

        // Vérifie les rôles spécifiques
        return $this->hasAnyRole($roles);
    }

    /**
     * Retourne l'affichage du rôle principal de l'utilisateur
     */
    #[Groups(['user:read', 'mission:users'])]
    public function getRoleDisplay(): string
    {
        foreach ($this->getRoles() as $role) {
            if (strpos($role, 'ROLE_MANAGER_') === 0) {
                $department = $this->extractDepartmentFromRole($role);
                return 'Manager - ' . $department;
            }
            if (strpos($role, 'ROLE_USER_') === 0) {
                $department = $this->extractDepartmentFromRole($role);
                return 'Utilisateur - ' . $department;
            }
        }

        return 'Utilisateur';
    }

    /**
     * Extrait le département lisible depuis un rôle
     */
    private function extractDepartmentFromRole(string $role): string
    {
        // Supprime ROLE_USER_ ou ROLE_MANAGER_ du début
        $department = preg_replace('/^ROLE_(USER|MANAGER)_/', '', $role);

        // Remplace les underscores par des espaces et met en forme
        $department = str_replace('_', ' ', $department);
        $department = ucwords(strtolower($department));

        if ($department === 'DEFAULT') {
            return $this->secteur ?? 'Non défini';
        }

        return $department;
    }

    // Méthode toArray pour la compatibilité
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'nom' => $this->nom,
            'prenom' => $this->prenom,
            'email' => $this->email,
            'roles' => $this->roles,
            'horaireHebdo' => $this->horaireHebdo,
            'forfaitJour' => $this->forfaitJour,
            'telephone' => $this->telephone,
            'dateEmbauche' => $this->dateEmbauche?->format('Y-m-d'),
            'dateDepart' => $this->dateDepart?->format('Y-m-d'),
            'actif' => $this->actif,
            'notes' => $this->notes,
            'secteur' => $this->secteur,
            'username' => $this->username,
            'manager' => $this->manager,
            'titre' => $this->titre,
            'isManager' => $this->isManager,
            'vpn' => $this->vpn,
            'mobile' => $this->mobile,
            'userOsi' => $this->userOsi,
        ];
    }

    /**
     * Méthode utilisée uniquement pour la création manuelle d'objets User
     */
    public function setId(int $id): static
    {
        $this->id = $id;
        return $this;
    }
}
